# GPU模型按条编辑功能验证清单

## 功能验证步骤

### 1. 前端界面验证

#### 1.1 进入GPU详情页面
- [ ] 打开浏览器访问 http://localhost:3000
- [ ] 进入"模型存储" -> "GPU管理"页面
- [ ] 点击任意GPU卡片进入详情页面
- [ ] 确认详情页面正常打开

#### 1.2 添加模型按钮显示
- [ ] 在"关联模型"卡片右上角应该显示"添加模型"按钮
- [ ] 按钮图标为加号图标（+）
- [ ] 按钮颜色为蓝色（#1890ff）

#### 1.3 模型标签显示
- [ ] 手动录入的模型以标签形式显示
- [ ] 模型标签使用蓝色渐变背景
- [ ] 悬停在模型标签上时不显示操作按钮（需要悬停在整个区域）

#### 1.4 权限控制验证
- [ ] 如果用户有`model_storage.gpu_management.edit`权限，应显示添加按钮和操作按钮
- [ ] 如果用户没有该权限，不应显示任何编辑相关按钮

### 2. 添加模型功能验证

#### 2.1 进入添加模式
- [ ] 点击"添加模型"按钮
- [ ] 显示输入框和保存/取消按钮
- [ ] 输入框占位符文字为"输入模型名称"
- [ ] 整个添加区域有浅灰色背景和边框

#### 2.2 正常添加
- [ ] 在输入框中输入新模型名称（如"TestModel1"）
- [ ] 点击保存按钮（绿色对勾图标）
- [ ] 新模型标签立即显示在列表中
- [ ] 添加区域自动消失
- [ ] 页面不需要刷新

#### 2.3 回车键添加
- [ ] 在输入框中输入新模型名称
- [ ] 按回车键
- [ ] 新模型标签立即显示在列表中
- [ ] 添加区域自动消失

#### 2.4 取消添加
- [ ] 点击取消按钮（红色×图标）
- [ ] 添加区域消失
- [ ] 输入的内容被清空

#### 2.5 重复模型验证
- [ ] 尝试添加已存在的模型名称
- [ ] 应显示错误提示"该模型名称已存在"
- [ ] 不应添加重复的模型标签
- [ ] 添加区域保持显示状态

#### 2.6 空输入验证
- [ ] 输入框为空时点击保存按钮
- [ ] 应显示错误提示"请输入模型名称"
- [ ] 不应添加空的模型标签
- [ ] 添加区域保持显示状态

### 3. 编辑模型功能验证

#### 3.1 显示编辑按钮
- [ ] 悬停在模型标签区域上
- [ ] 应显示编辑按钮（铅笔图标）和删除按钮（垃圾桶图标）
- [ ] 按钮透明度从0变为1，有平滑过渡效果
- [ ] 移开鼠标后按钮重新隐藏

#### 3.2 进入编辑模式
- [ ] 点击编辑按钮
- [ ] 模型标签变为输入框
- [ ] 输入框中显示当前模型名称
- [ ] 显示保存和取消按钮
- [ ] 整个编辑区域有浅灰色背景和边框

#### 3.3 正常编辑
- [ ] 修改输入框中的模型名称
- [ ] 点击保存按钮（绿色对勾图标）
- [ ] 模型标签显示新的名称
- [ ] 编辑区域变回正常的标签显示
- [ ] 显示成功提示

#### 3.4 回车键保存
- [ ] 在编辑输入框中修改模型名称
- [ ] 按回车键
- [ ] 模型标签显示新的名称
- [ ] 编辑区域变回正常的标签显示

#### 3.5 取消编辑
- [ ] 点击取消按钮（红色×图标）
- [ ] 编辑区域变回正常的标签显示
- [ ] 模型名称恢复为原来的值
- [ ] 不显示任何提示

#### 3.6 重复名称验证
- [ ] 尝试将模型名称改为已存在的其他模型名称
- [ ] 应显示错误提示"该模型名称已存在"
- [ ] 模型名称不应被修改
- [ ] 编辑区域保持显示状态

#### 3.7 空名称验证
- [ ] 将输入框内容清空
- [ ] 点击保存按钮
- [ ] 应显示错误提示"请输入模型名称"
- [ ] 模型名称不应被修改
- [ ] 编辑区域保持显示状态

### 4. 删除模型功能验证

#### 4.1 删除操作
- [ ] 悬停在模型标签区域上显示操作按钮
- [ ] 点击删除按钮（垃圾桶图标）
- [ ] 该模型标签立即从列表中消失
- [ ] 页面不需要刷新
- [ ] 显示成功提示
- [ ] 其他模型标签不受影响

#### 4.2 删除确认
- [ ] 删除操作应立即生效，无需确认对话框
- [ ] 删除后无法撤销（符合预期）

### 5. 并发操作验证

#### 5.1 同时编辑限制
- [ ] 尝试同时编辑多个模型
- [ ] 应该只能有一个模型处于编辑状态
- [ ] 开始编辑新模型时，之前的编辑应该被取消

#### 5.2 编辑与添加冲突
- [ ] 在编辑模型时点击"添加模型"按钮
- [ ] 应该能正常进入添加模式
- [ ] 编辑状态应该被取消

### 6. 数据持久化验证

#### 6.1 数据保存
- [ ] 执行添加、编辑或删除操作后，关闭详情页面
- [ ] 重新打开同一GPU的详情页面
- [ ] 确认模型变更已保存
- [ ] 模型列表显示最新状态

#### 6.2 API调用验证
- [ ] 打开浏览器开发者工具（F12）
- [ ] 切换到Network标签
- [ ] 执行添加/编辑/删除操作
- [ ] 确认发送了PATCH请求到`/api/model-storage/gpus/{id}/`
- [ ] 确认请求包含`manual_models`字段
- [ ] 确认响应状态码为200

### 7. 样式和用户体验验证

#### 7.1 视觉效果
- [ ] 模型标签有正确的蓝色渐变背景
- [ ] 悬停时操作按钮有平滑的透明度过渡
- [ ] 编辑和添加区域有统一的浅灰色背景样式
- [ ] 输入框有聚焦效果（边框颜色和阴影）
- [ ] 保存和取消按钮有正确的颜色（绿色和红色）

#### 7.2 响应式设计
- [ ] 在不同屏幕尺寸下界面正常显示
- [ ] 移动设备上操作正常
- [ ] 标签和编辑区域换行显示正常

### 8. 错误处理验证

#### 8.1 网络错误
- [ ] 断开网络连接
- [ ] 尝试添加/编辑/删除模型
- [ ] 应显示网络错误提示
- [ ] 界面状态保持稳定

#### 8.2 服务器错误
- [ ] 模拟服务器500错误
- [ ] 应显示服务器错误提示
- [ ] 界面状态保持稳定

## 验证结果记录

### 通过的测试项
- [ ] 所有界面元素正常显示
- [ ] 添加模型功能正常
- [ ] 编辑模型功能正常
- [ ] 删除模型功能正常
- [ ] 并发操作控制正常
- [ ] 数据持久化正常
- [ ] 权限控制正常
- [ ] 样式效果正常
- [ ] 错误处理正常

### 发现的问题
记录测试过程中发现的任何问题：

1. 
2. 
3. 

### 总体评估
- [ ] 功能完整性：✅ 完整 / ⚠️ 部分完整 / ❌ 不完整
- [ ] 用户体验：✅ 良好 / ⚠️ 一般 / ❌ 较差
- [ ] 稳定性：✅ 稳定 / ⚠️ 基本稳定 / ❌ 不稳定
- [ ] 性能：✅ 良好 / ⚠️ 一般 / ❌ 较差

## 备注
- 测试环境：
- 测试时间：
- 测试人员：
