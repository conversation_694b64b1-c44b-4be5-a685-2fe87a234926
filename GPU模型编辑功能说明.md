# GPU模型编辑功能说明（按条编辑版本）

## 功能概述

为GPU管理系统的详情页面添加了手动录入模型的逐条编辑功能，用户现在可以直接在GPU详情页面中对每个模型进行单独的编辑、删除操作，以及添加新模型。

## 功能特性

### 1. 逐条编辑模式
- 每个手动录入的模型标签都有独立的编辑和删除按钮
- 悬停在模型标签上时显示操作按钮（编辑、删除）
- 点击编辑按钮后该模型进入编辑状态，其他模型保持正常显示

### 2. 添加新模型
- 在"关联模型"卡片右上角添加了"添加模型"按钮
- 点击后显示输入框和保存/取消按钮
- 用户可以输入模型名称并点击保存按钮或按回车键添加
- 自动验证重复模型名称，避免重复添加
- 添加成功后会实时更新显示

### 3. 编辑模型
- 点击模型标签旁的编辑按钮进入编辑模式
- 模型名称变为可编辑的输入框
- 显示保存和取消按钮
- 支持回车键快速保存
- 自动验证重复模型名称

### 4. 删除模型
- 每个手动录入的模型标签都有删除按钮
- 点击删除按钮可立即移除对应的模型
- 删除操作会立即生效并更新显示

### 4. 数据同步
- 所有编辑操作都会实时同步到后端数据库
- 使用PATCH请求更新GPU的manual_models字段
- 前端状态会实时更新，无需刷新页面

## 技术实现

### 前端实现
1. **状态管理**：
   - 添加`editingModels`状态控制编辑模式
   - 添加`newModelName`状态管理新模型输入

2. **API调用**：
   - 使用`http.patch()`方法更新GPU数据
   - 请求路径：`/api/model-storage/gpus/${gpu.id}/`
   - 更新字段：`manual_models`（逗号分隔的字符串）

3. **用户体验**：
   - 添加了输入验证和错误提示
   - 支持回车键快速添加
   - 实时更新本地状态避免重新请求

### 后端支持
- 利用现有的GPU PATCH接口
- manual_models字段存储逗号分隔的模型名称
- to_dict()方法自动将其转换为tested_models_manual数组

### 样式设计
- 编辑按钮使用图标切换（编辑/关闭）
- 模型标签支持删除状态的视觉反馈
- 输入框和添加按钮采用现代化设计
- 响应式布局适配不同屏幕尺寸

## 使用方法

### 添加新模型
1. 进入GPU管理页面
2. 点击任意GPU卡片进入详情页面
3. 在"关联模型"卡片右上角点击"添加模型"按钮
4. 在输入框中输入新模型名称
5. 点击保存按钮或按回车键确认添加
6. 点击取消按钮或×按钮取消添加

### 编辑现有模型
1. 悬停在要编辑的模型标签上
2. 点击出现的编辑按钮（铅笔图标）
3. 在输入框中修改模型名称
4. 点击保存按钮或按回车键确认修改
5. 点击取消按钮或×按钮取消修改

### 删除模型
1. 悬停在要删除的模型标签上
2. 点击出现的删除按钮（垃圾桶图标）
3. 模型立即被删除

## 注意事项

- 只能编辑手动录入的模型，自动关联的模型不可编辑
- 模型名称不能重复
- 所有操作都会立即保存到数据库
- 编辑功能需要相应的权限（model_storage.gpu_management.edit）
