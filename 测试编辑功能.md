# 测试编辑功能步骤

## 问题排查

### 1. 权限问题已修复
- ✅ 权限名称从 `model_storage.test_task.edit` 修正为 `model_storage.test_tasks.edit`
- ✅ 添加了调试信息来检查权限状态

### 2. 当前测试步骤

1. **打开浏览器开发者工具**
   - 按F12打开开发者工具
   - 切换到Console标签页

2. **进入GPU详情页面**
   - 访问GPU管理页面
   - 点击任意GPU卡片进入详情页面

3. **检查操作列是否显示**
   - 查看"相关测试任务"表格最右侧是否有"操作"列
   - 如果没有显示，查看控制台输出的权限检查结果

4. **测试编辑按钮**
   - 点击蓝色编辑按钮
   - 查看控制台是否输出调试信息
   - 检查编辑弹窗是否正常弹出

### 3. 可能的问题和解决方案

#### 如果操作列不显示
```javascript
// 在控制台中执行以下代码检查权限
import { hasPermission } from 'libs';
console.log('权限检查结果:', hasPermission('model_storage.test_tasks.edit'));
```

#### 如果编辑按钮点击无反应
1. 检查控制台是否有JavaScript错误
2. 确认handleEditTask函数是否被调用
3. 检查editTaskVisible状态是否正确设置

#### 如果弹窗不显示
1. 检查Modal组件是否正确渲染
2. 确认editTaskVisible状态为true
3. 检查CSS样式是否有冲突

### 4. 调试信息说明

当前代码中添加了以下调试信息：
- `操作列权限检查: true/false` - 显示用户是否有编辑权限
- `编辑测试任务: {任务对象}` - 显示被编辑的任务数据
- `权限检查: true/false` - 显示编辑时的权限状态

### 5. 预期行为

**正常情况下应该看到：**
1. 表格右侧显示"操作"列
2. 每行有蓝色编辑按钮和红色删除按钮
3. 点击编辑按钮后弹出编辑弹窗
4. 弹窗中预填充当前任务的数据
5. 可以修改各个字段并保存

**如果权限不足：**
1. 不显示"操作"列
2. 控制台显示权限检查为false

### 6. 下一步操作

如果测试发现问题，请提供：
1. 浏览器控制台的错误信息
2. 权限检查的结果
3. 具体的异常行为描述

这样我可以进一步定位和修复问题。
