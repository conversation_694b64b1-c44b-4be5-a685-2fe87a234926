# GPU测试任务编辑功能说明

## 功能概述

为GPU详情页面的"相关测试任务"表格添加了编辑和删除功能，用户现在可以直接在表格中对测试任务进行管理操作。

## 功能特性

### 1. 操作列
- 在测试任务表格最右侧添加了"操作"列
- 每行显示编辑和删除按钮
- 按钮采用链接样式，节省空间
- 只有具有相应权限的用户才能看到操作按钮

### 2. 编辑功能
- **编辑按钮**: 蓝色铅笔图标
- **编辑弹窗**: 点击后打开编辑任务的Modal弹窗
- **可编辑字段**: 模型名称、测试人员、模型类型、优先级、测试状态、开始日期、结束日期、备注
- **表单验证**: 模型名称和测试人员为必填字段
- **权限控制**: 需要`model_storage.test_tasks.edit`权限
- **即时更新**: 保存成功后自动刷新任务列表

### 3. 删除功能
- **删除按钮**: 红色垃圾桶图标
- **确认对话框**: 点击后显示确认删除对话框
- **安全提示**: 显示要删除的任务名称进行确认
- **即时更新**: 删除成功后自动刷新任务列表

## 技术实现

### 表格列配置
```javascript
{
  title: '操作',
  key: 'actions',
  width: 120,
  render: (_, record) => (
    hasPermission('model_storage.test_tasks.edit') && (
      <div style={{ display: 'flex', gap: 8 }}>
        <Button
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEditTask(record)}
          style={{ color: '#1890ff', padding: 0 }}
        />
        <Button
          type="link"
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteTask(record)}
          style={{ color: '#ff4d4f', padding: 0 }}
        />
      </div>
    )
  )
}
```

### 编辑处理函数
```javascript
const handleEditTask = (task) => {
  setEditingTask(task);
  editTaskForm.setFieldsValue({
    model_name: task.model_name,
    tester: task.tester,
    model_type: task.model_type,
    priority: task.priority,
    test_status: task.test_status,
    start_date: task.start_date,
    end_date: task.end_date,
    notes: task.notes
  });
  setEditTaskVisible(true);
};

const handleSaveEditTask = () => {
  editTaskForm.validateFields().then(values => {
    const payload = {
      ...values,
      id: editingTask.id
    };

    http.patch(`/api/model-storage/test-tasks/${editingTask.id}/`, payload)
      .then(() => {
        message.success('修改成功');
        setEditTaskVisible(false);
        setEditingTask(null);
        editTaskForm.resetFields();
        fetchTasks();
      })
      .catch(() => {
        message.error('修改失败');
      });
  });
};
```

### 删除处理函数
```javascript
const handleDeleteTask = (task) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除测试任务 "${task.model_name}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      http.delete(`/api/model-storage/test-tasks/${task.id}/`)
        .then(() => {
          message.success('删除成功');
          fetchTasks(); // 重新加载任务列表
        })
        .catch(() => {
          message.error('删除失败');
        });
    }
  });
};
```

### 数据刷新函数
```javascript
const fetchTasks = () => {
  if (gpu.name) {
    setLoading(true);
    http.get('/api/model-storage/test-tasks/', { params: { gpu_model: gpu.name } })
      .then(res => setTasks(res))
      .finally(() => setLoading(false));
  }
};
```

## 权限控制

### 前端权限检查
- 使用`hasPermission('model_storage.test_tasks.edit')`检查权限
- 只有有权限的用户才能看到操作按钮
- 无权限用户看到的表格不显示操作列

### 后端权限验证
- 删除API需要相应的后端权限验证
- 确保API调用的安全性

## 用户体验

### 视觉设计
- **编辑按钮**: 蓝色图标，表示可编辑操作
- **删除按钮**: 红色图标，表示危险操作
- **按钮间距**: 8px间距，保持视觉平衡
- **链接样式**: 使用link类型按钮，减少视觉干扰

### 交互体验
- **即时反馈**: 操作后立即显示成功或失败消息
- **确认机制**: 删除操作有确认对话框，防止误操作
- **自动刷新**: 删除成功后自动刷新列表，保持数据同步

## API接口

### 删除测试任务
- **请求方式**: DELETE
- **请求路径**: `/api/model-storage/test-tasks/{id}/`
- **权限要求**: 需要测试任务编辑权限
- **响应**: 成功返回200状态码

### 获取任务列表
- **请求方式**: GET
- **请求路径**: `/api/model-storage/test-tasks/`
- **查询参数**: `gpu_model` - GPU模型名称
- **响应**: 返回任务列表数组

## 使用方法

### 删除测试任务
1. 进入GPU管理页面
2. 点击任意GPU卡片进入详情页面
3. 在"相关测试任务"表格中找到要删除的任务
4. 点击该行右侧的删除按钮（红色垃圾桶图标）
5. 在确认对话框中点击"确定"
6. 系统显示删除成功消息，任务从列表中消失

### 编辑测试任务
1. 进入GPU管理页面
2. 点击任意GPU卡片进入详情页面
3. 在"相关测试任务"表格中找到要编辑的任务
4. 点击该行右侧的编辑按钮（蓝色铅笔图标）
5. 在弹出的编辑弹窗中修改任务信息
6. 点击"保存"按钮确认修改
7. 系统显示修改成功消息，任务信息更新

## 编辑弹窗详细功能

### 可编辑字段
1. **基本信息**
   - 模型名称（必填）
   - 测试人员（必填）
   - 模型类型（下拉选择：推理、训练、微调、预训练、优化）
   - 优先级（下拉选择：P1-高、P2-中、P3-低）

2. **状态和时间**
   - 测试状态（下拉选择：待开始、进行中、已完成、已取消、阻塞中、已延期）
   - 开始日期（日期选择器）
   - 结束日期（日期选择器）

3. **其他信息**
   - 备注（多行文本输入）

### 表单验证
- 模型名称和测试人员为必填字段
- 日期格式自动验证
- 表单提交前进行完整性检查

## 后续开发计划

### 批量操作
1. **批量删除**
   - 添加表格行选择功能
   - 批量删除确认对话框
   - 批量API调用

2. **批量编辑**
   - 选择多个任务进行批量编辑
   - 支持批量修改状态、优先级等字段

### 高级功能
1. **任务复制**
   - 基于现有任务创建新任务
   - 快速创建相似任务

2. **任务导出**
   - 导出任务列表为Excel
   - 支持筛选条件导出

## 测试建议

### 功能测试
- [ ] 验证操作列正确显示
- [ ] 测试编辑按钮点击效果
- [ ] 测试删除功能完整流程
- [ ] 验证权限控制正确性
- [ ] 测试数据刷新机制

### 兼容性测试
- [ ] 不同浏览器兼容性
- [ ] 移动端响应式显示
- [ ] 不同权限用户的显示效果

### 性能测试
- [ ] 大量任务数据的加载性能
- [ ] 删除操作的响应速度
- [ ] 页面刷新的流畅性

## 总结

GPU测试任务编辑功能已成功添加删除功能，为用户提供了便捷的任务管理能力。编辑功能的框架已搭建完成，后续可以根据具体需求进行详细开发。

**当前状态**:
- ✅ 删除功能完整实现
- ✅ 编辑功能完整实现
- ✅ 权限控制完善
- ✅ 用户体验良好
- 🔄 批量操作待开发

功能现已准备就绪，建议进行全面测试后投入使用。
