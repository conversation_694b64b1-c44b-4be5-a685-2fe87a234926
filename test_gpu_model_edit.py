#!/usr/bin/env python3
"""
GPU模型编辑功能测试脚本
测试GPU手动录入模型的增删改查功能
"""

import urllib.request
import urllib.parse
import json
import sys

# 配置
BASE_URL = "http://localhost:9999"
API_BASE = f"{BASE_URL}/api/model-storage"

def test_gpu_model_edit():
    """测试GPU模型编辑功能"""
    
    print("=== GPU模型编辑功能测试 ===\n")
    
    # 1. 获取GPU列表
    print("1. 获取GPU列表...")
    try:
        response = requests.get(f"{API_BASE}/gpus/", params={"token": "1"}, timeout=10)
        print(f"   响应状态码: {response.status_code}")
        if response.status_code == 200:
            gpus = response.json()
            if not gpus:
                print("❌ 没有找到GPU设备，请先创建GPU设备")
                return False
            
            gpu = gpus[0]  # 使用第一个GPU进行测试
            gpu_id = gpu['id']
            gpu_name = gpu['name']
            print(f"✅ 找到GPU: {gpu_name} (ID: {gpu_id})")
            print(f"   当前手动录入模型: {gpu.get('tested_models_manual', [])}")
        else:
            print(f"❌ 获取GPU列表失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        print(f"   请确保spug服务正在运行在 {BASE_URL}")
        return False
    
    # 2. 测试添加模型
    print("\n2. 测试添加模型...")
    test_models = ["TestModel1", "TestModel2", "TestModel3"]
    current_models = gpu.get('manual_models', '').split(',') if gpu.get('manual_models') else []
    current_models = [m.strip() for m in current_models if m.strip()]
    
    # 添加测试模型
    new_models = list(set(current_models + test_models))
    manual_models_str = ','.join(new_models)
    
    try:
        response = requests.patch(
            f"{API_BASE}/gpus/{gpu_id}/",
            json={"manual_models": manual_models_str},
            params={"token": "1"}
        )
        if response.status_code == 200:
            updated_gpu = response.json()
            print(f"✅ 成功添加模型")
            print(f"   更新后的手动录入模型: {updated_gpu.get('tested_models_manual', [])}")
        else:
            print(f"❌ 添加模型失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 3. 测试删除模型
    print("\n3. 测试删除模型...")
    if new_models:
        # 删除第一个测试模型
        model_to_delete = test_models[0]
        remaining_models = [m for m in new_models if m != model_to_delete]
        manual_models_str = ','.join(remaining_models)
        
        try:
            response = requests.patch(
                f"{API_BASE}/gpus/{gpu_id}/",
                json={"manual_models": manual_models_str},
                params={"token": "1"}
            )
            if response.status_code == 200:
                updated_gpu = response.json()
                print(f"✅ 成功删除模型: {model_to_delete}")
                print(f"   更新后的手动录入模型: {updated_gpu.get('tested_models_manual', [])}")
            else:
                print(f"❌ 删除模型失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    # 4. 测试获取单个GPU详情
    print("\n4. 测试获取GPU详情...")
    try:
        response = requests.get(f"{API_BASE}/gpus/{gpu_id}/", params={"token": "1"})
        if response.status_code == 200:
            gpu_detail = response.json()
            print(f"✅ 成功获取GPU详情")
            print(f"   GPU名称: {gpu_detail['name']}")
            print(f"   厂商: {gpu_detail['vendor']}")
            print(f"   手动录入模型: {gpu_detail.get('tested_models_manual', [])}")
            print(f"   自动关联模型: {gpu_detail.get('tested_models_auto', [])}")
            print(f"   所有模型: {gpu_detail.get('tested_models', [])}")
        else:
            print(f"❌ 获取GPU详情失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 5. 清理测试数据
    print("\n5. 清理测试数据...")
    original_models = [m for m in current_models if m not in test_models]
    manual_models_str = ','.join(original_models)
    
    try:
        response = requests.patch(
            f"{API_BASE}/gpus/{gpu_id}/",
            json={"manual_models": manual_models_str},
            params={"token": "1"}
        )
        if response.status_code == 200:
            print("✅ 测试数据清理完成")
        else:
            print(f"⚠️  清理测试数据失败: {response.status_code}")
    except Exception as e:
        print(f"⚠️  清理请求失败: {e}")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    success = test_gpu_model_edit()
    sys.exit(0 if success else 1)
