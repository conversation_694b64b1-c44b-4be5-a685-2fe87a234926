import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Drawer, Spin, Table, Tag, Descriptions, Button, Modal, Form, Input, Select, message, Card, Row, Col, Statistic, Progress } from 'antd';
import { PlusOutlined, <PERSON>boltOutlined, ExperimentOutlined, UserOutlined, CalendarOutlined, FileTextOutlined, EditOutlined, DeleteOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { hasPermission } from 'libs';
import http from 'libs/http';
import TestStepsSidebar from '../../exec/test-case-sets/TestStepsSidebar';
import styles from './Detail.module.less';

const MODEL_TYPES = [
  { value: 'inference', label: '推理' },
  { value: 'training', label: '训练' },
  { value: 'fine_tuning', label: '微调' },
  { value: 'pre-training', label: '预训练' },
  { value: 'optimization', label: '优化' },
];

function GpuDetail({ store }) {
  const [loading, setLoading] = useState(true);
  const [tasks, setTasks] = useState([]);
  const [formVisible, setFormVisible] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [form] = Form.useForm();
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [selectedCaseSet, setSelectedCaseSet] = useState(null);
  const [editingModels, setEditingModels] = useState(false);
  const [newModelName, setNewModelName] = useState('');
  const gpu = store.record;

  useEffect(() => {
    if (gpu.name) {
      setLoading(true);
      http.get('/api/model-storage/test-tasks/', { params: { gpu_model: gpu.name } })
        .then(res => setTasks(res))
        .finally(() => setLoading(false));
    }
  }, [gpu.name, refreshKey]);

  const handleAddTask = () => {
    form.validateFields().then(values => {
      const payload = {
        ...values,
        gpu_model: gpu.name,
      };
      http.post('/api/model-storage/test-tasks/', payload)
        .then(() => {
          message.success('添加成功');
          setFormVisible(false);
          form.resetFields();
          setRefreshKey(prev => prev + 1); // 触发刷新
        });
    });
  };

  // 处理测试用例集点击
  const handleCaseSetClick = async (task) => {
    if (!task.test_case_set_info) {
      message.warning('该任务未关联测试用例集');
      return;
    }

    try {
      // 获取完整的测试用例集信息
      const response = await http.get(`/api/exec/test-case-sets/${task.test_case_set_info.id}/`);
      const caseSetData = response.data || response;

      // 将任务的步骤完成状态和进度合并到用例集数据中
      const mergedData = {
        ...caseSetData,
        step_completion_status: task.step_completion_status || {},
        progress: task.progress || 0,
        task_id: task.id  // 保存任务ID用于更新
      };

      setSelectedCaseSet(mergedData);
      setSidebarVisible(true);
    } catch (error) {
      console.error('获取测试用例集详情失败:', error);
      message.error('获取测试用例集详情失败');
    }
  };

  // 更新测试用例集进度
  const handleCaseSetUpdate = (updatedData) => {
    setSelectedCaseSet(updatedData);
    // 刷新任务列表以显示最新进度
    setRefreshKey(prev => prev + 1);
  };

  // 添加新模型
  const handleAddModel = () => {
    if (!newModelName.trim()) {
      message.warning('请输入模型名称');
      return;
    }

    const currentModels = gpu.tested_models_manual || [];
    if (currentModels.includes(newModelName.trim())) {
      message.warning('该模型已存在');
      return;
    }

    const updatedModels = [...currentModels, newModelName.trim()];
    const manual_models = updatedModels.join(',');

    http.patch(`/api/model-storage/gpus/${gpu.id}/`, { manual_models })
      .then(() => {
        message.success('添加成功');
        setNewModelName('');
        // 更新本地数据
        store.record = {
          ...store.record,
          manual_models,
          tested_models_manual: updatedModels
        };
      })
      .catch(() => {
        message.error('添加失败');
      });
  };

  // 删除模型
  const handleDeleteModel = (modelToDelete) => {
    const currentModels = gpu.tested_models_manual || [];
    const updatedModels = currentModels.filter(model => model !== modelToDelete);
    const manual_models = updatedModels.join(',');

    http.patch(`/api/model-storage/gpus/${gpu.id}/`, { manual_models })
      .then(() => {
        message.success('删除成功');
        // 更新本地数据
        store.record = {
          ...store.record,
          manual_models,
          tested_models_manual: updatedModels
        };
      })
      .catch(() => {
        message.error('删除失败');
      });
  };

  const columns = [
    { title: '模型名称', dataIndex: 'model_name', key: 'model_name' },
    { title: '测试人员', dataIndex: 'tester', key: 'tester' },
    { title: '模型类型', dataIndex: 'model_type_display', key: 'model_type_display' },
    { title: '优先级', dataIndex: 'priority_display', key: 'priority_display' },
    { title: '测试状态', dataIndex: 'test_status_display', key: 'test_status_display', render: text => <Tag>{text}</Tag> },
    {
      title: '测试用例集',
      key: 'test_case_set',
      render: (_, record) => {
        if (record.test_case_set_info) {
          return (
            <div style={{ cursor: 'pointer' }} onClick={() => handleCaseSetClick(record)}>
              <Tag
                color="blue"
                icon={<FileTextOutlined />}
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  ':hover': { backgroundColor: '#1890ff' }
                }}
              >
                {record.test_case_set_info.name}
              </Tag>
              {record.test_case_set_info.progress_percentage !== undefined && (
                <div style={{ marginTop: 4, width: 80 }}>
                  <Progress
                    percent={record.test_case_set_info.progress_percentage}
                    size="small"
                    strokeColor="#52c41a"
                    showInfo={false}
                  />
                  <div style={{ fontSize: '11px', color: '#666', textAlign: 'center' }}>
                    {record.test_case_set_info.progress_percentage}%
                  </div>
                </div>
              )}
            </div>
          );
        }
        return <Tag color="default">未关联</Tag>;
      }
    },
    { title: '开始日期', dataIndex: 'start_date', key: 'start_date' },
    { title: '结束日期', dataIndex: 'end_date', key: 'end_date' },
  ];

  // 计算统计数据
  const getTaskStats = () => {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const runningTasks = tasks.filter(task => task.status === 'running').length;
    const failedTasks = tasks.filter(task => task.status === 'failed').length;
    return { totalTasks, completedTasks, runningTasks, failedTasks };
  };

  const taskStats = getTaskStats();

  return (
    <>
      <Drawer
        title={
          <div className={styles.drawerTitle}>
            <ThunderboltOutlined style={{ marginRight: 8, color: '#667eea' }} />
            GPU设备详情: {gpu.name}
          </div>
        }
        placement="right"
        width={900}
        onClose={() => store.detailVisible = false}
        visible={store.detailVisible}
        className={styles.gpuDetailDrawer}
      >
        <Spin spinning={loading}>
          {/* 基本信息卡片 */}
          <Card className={styles.infoCard} title="基本信息">
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>GPU名称</span>
                  <span className={styles.infoValue}>{gpu.name}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>厂商</span>
                  <Tag className={styles.vendorTag}>{gpu.vendor}</Tag>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>测试模型总数</span>
                  <span className={styles.infoValue}>{gpu.tested_models_count || 0}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>创建时间</span>
                  <span className={styles.infoValue}>{gpu.created_at}</span>
                </div>
              </Col>
              {gpu.description && (
                <Col span={24}>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>设备描述</span>
                    <div className={styles.description}>{gpu.description}</div>
                  </div>
                </Col>
              )}
            </Row>
          </Card>

          {/* 模型信息卡片 */}
          <Card
            className={styles.modelCard}
            title="关联模型"
            extra={
              hasPermission('model_storage.gpu_management.edit') && (
                <Button
                  type="link"
                  icon={editingModels ? <CloseOutlined /> : <EditOutlined />}
                  onClick={() => setEditingModels(!editingModels)}
                  style={{ color: editingModels ? '#ff4d4f' : '#1890ff' }}
                >
                  {editingModels ? '取消编辑' : '编辑模型'}
                </Button>
              )
            }
          >
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <div className={styles.modelSection}>
                  <h4>手动录入模型</h4>
                  <div className={styles.modelTags}>
                    {gpu.tested_models_manual && gpu.tested_models_manual.length > 0 ?
                      gpu.tested_models_manual.map(model => (
                        <Tag
                          key={model}
                          className={styles.manualModelTag}
                          closable={editingModels}
                          onClose={() => handleDeleteModel(model)}
                        >
                          {model}
                        </Tag>
                      )) :
                      <span className={styles.emptyText}>暂无手动录入模型</span>
                    }

                    {/* 添加新模型的输入框 */}
                    {editingModels && (
                      <div style={{ marginTop: 8, display: 'flex', gap: 8, alignItems: 'center' }}>
                        <Input
                          placeholder="输入模型名称"
                          value={newModelName}
                          onChange={(e) => setNewModelName(e.target.value)}
                          onPressEnter={handleAddModel}
                          style={{ width: 200 }}
                          size="small"
                          className={styles.modelEditInput}
                        />
                        <Button
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={handleAddModel}
                          className={styles.addModelButton}
                        >
                          添加
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </Col>
              <Col span={24}>
                <div className={styles.modelSection}>
                  <h4>自动关联模型</h4>
                  <div className={styles.modelTags}>
                    {gpu.tested_models_auto && gpu.tested_models_auto.length > 0 ?
                      gpu.tested_models_auto.map(model => (
                        <Tag key={model} className={styles.autoModelTag}>{model}</Tag>
                      )) :
                      <span className={styles.emptyText}>暂无自动关联模型</span>
                    }
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 任务统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="总任务数"
                  value={taskStats.totalTasks}
                  prefix={<ExperimentOutlined style={{ color: '#1890ff' }} />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="已完成"
                  value={taskStats.completedTasks}
                  prefix={<ExperimentOutlined style={{ color: '#52c41a' }} />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="运行中"
                  value={taskStats.runningTasks}
                  prefix={<ExperimentOutlined style={{ color: '#fa8c16' }} />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="失败"
                  value={taskStats.failedTasks}
                  prefix={<ExperimentOutlined style={{ color: '#ff4d4f' }} />}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 测试任务表格 */}
          <Card
            className={styles.taskCard}
            title="相关测试任务"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setFormVisible(true)}
                className={styles.gradientButton}
              >
                新增任务
              </Button>
            }
          >
            <Table
              rowKey="id"
              dataSource={tasks}
              columns={columns}
              pagination={{ pageSize: 10 }}
              className={styles.taskTable}
            />
          </Card>
        </Spin>
      </Drawer>

      <Modal
        title={
          <div className={styles.modalTitle}>
            <ExperimentOutlined style={{ marginRight: 8, color: '#667eea' }} />
            新增测试任务
          </div>
        }
        visible={formVisible}
        onCancel={() => setFormVisible(false)}
        maskClosable={false}
        destroyOnClose
        width={600}
        className={styles.taskModal}
        footer={[
          <Button key="cancel" onClick={() => setFormVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleAddTask}
            className={styles.gradientButton}
          >
            创建任务
          </Button>
        ]}
      >
        <Form form={form} layout="vertical" className={styles.taskForm}>
          <Form.Item label="GPU型号">
            <Input
              value={gpu.name}
              disabled
              size="large"
              prefix={<ThunderboltOutlined />}
            />
          </Form.Item>
          <Form.Item
            label="模型名称"
            name="model_name"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input
              placeholder="请输入要测试的模型名称"
              size="large"
              prefix={<ExperimentOutlined />}
            />
          </Form.Item>
          <Form.Item
            label="测试人员"
            name="tester"
            rules={[{ required: true, message: '请输入测试人员姓名' }]}
          >
            <Input
              placeholder="请输入测试人员姓名"
              size="large"
              prefix={<UserOutlined />}
            />
          </Form.Item>
          <Form.Item
            label="模型类型"
            name="model_type"
            initialValue="inference"
          >
            <Select size="large" placeholder="请选择模型类型">
              {MODEL_TYPES.map(item => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试步骤侧边栏 */}
      <TestStepsSidebar
        visible={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
        caseSet={selectedCaseSet}
        onUpdate={handleCaseSetUpdate}
        taskId={selectedCaseSet?.task_id}
        mode="task"
      />
    </>
  );
}

export default observer(GpuDetail); 